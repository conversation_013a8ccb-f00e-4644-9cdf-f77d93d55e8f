import { LocalShareData } from "@src/types/types.ts";

// LocalShareData 测试数据
export const localShareDataTest: LocalShareData = {
  "projectInfo": {
      "projectName": "飞书文档",
      "projectId": "68061e273bba346cff2cc878"
  },
  "nodes": [
      
  ],
  "variables": [
      {
          "_id": "68146faa5c85e196ebd7ecdf",
          "name": "testUrl",
          "type": "string",
          "value": "http://127.0.0.1:7001",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "681ae1ec3bfc0bdc74d353c3"
          }
      },
      {
          "_id": "681ae553369b97411a1883f4",
          "name": "stringValue",
          "type": "string",
          "value": "http://127.0.0.1:7001",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "681ae5ce369b97411a188470"
          }
      },
      {
          "_id": "681ae562369b97411a188407",
          "name": "numberValue",
          "type": "number",
          "value": "-27.3",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "681ae562369b97411a188408"
          }
      },
      {
          "_id": "681ae56a369b97411a18841a",
          "name": "booleanValue",
          "type": "boolean",
          "value": "true",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "681ae56a369b97411a18841b"
          }
      },
      {
          "_id": "681ae573369b97411a18842d",
          "name": "nullValue",
          "type": "null",
          "value": "null",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "681ae573369b97411a18842e"
          }
      },
      {
          "_id": "681ae598369b97411a188440",
          "name": "fileValue",
          "type": "file",
          "value": "",
          "fileValue": {
              "name": "a.png",
              "fileType": "image/png",
              "path": "C:\\Users\\<USER>\\Pictures\\a.png",
              "_id": "682c5ae5aafe9a5b722d6d3e"
          }
      },
      {
          "_id": "681ae5ad369b97411a188453",
          "name": "anyValue",
          "type": "any",
          "value": "new Date().toLocaleString()",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "681ae5ad369b97411a188454"
          }
      },
      {
          "_id": "685a7ebc9414fc1a509ba57d",
          "name": "token",
          "type": "string",
          "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MDQ4OWEzMDAxZjA3NDYwY2EzMTFiMCIsInJvbGVJZHMiOlsiNWVkZTBiYTA2Zjc2MTg1MjA0NTg0NzAwIl0sImxvZ2luTmFtZSI6ImFwaWZsb3ciLCJyZWFsTmFtZSI6IuWIneWni-aZrumAmueUqOaItyIsInBob25lIjoiIiwidG9rZW4iOiIiLCJpYXQiOjE3NTE3MjIzNDcsImV4cCI6MTc1MjMyNzE0N30.Ei4yVmo_AgMCOkpHVandURkC9mqJ2SIsN0UzOIwG0DU",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "686938584e0f546e4f44f44d"
          }
      },
      {
          "_id": "685a827b25db291d8777609a",
          "name": "baseUrl",
          "type": "string",
          "value": "http://127.0.0.1:7001",
          "fileValue": {
              "name": "",
              "fileType": "",
              "path": "",
              "_id": "685a827b25db291d8777609b"
          }
      }
  ]
}

// 导出测试数据
export default localShareDataTest; 

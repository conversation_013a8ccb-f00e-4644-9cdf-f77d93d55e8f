export default [{
  title: '常用',
  values: [{
    alias: 'JSON',
    mimeType: 'application/json'
  }, {
    alias: 'SVG',
    mimeType: 'image/svg+xml'
  }, {
    alias: 'HTM,HTML',
    mimeType: 'text/html'
  }, {
    alias: 'CSV',
    mimeType: 'text/csv'
  }, {
    alias: 'GIF',
    mimeType: 'image/gif'
  }, {
    alias: 'JPEG JPG',
    mimeType: 'image/jpeg'
  }, {
    alias: 'PNG',
    mimeType: 'image/png'
  }, {
    alias: 'PDF',
    mimeType: 'application/pdf'
  }, {
    alias: 'application/octet-stream',
    mimeType: 'application/octet-stream'
  }, {
    alias: 'text/plain',
    mimeType: 'text/plain'
  }]
}, {
  title: '办公文件',
  values: [{
    alias: 'DOC',
    mimeType: 'application/msword'
  }, {
    alias: 'DOCX',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  }, {
    alias: 'XLS',
    mimeType: 'application/vnd.ms-excel'
  }, {
    alias: 'XLSX',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  }]
}, {
  title: '文本类型',
  values: [{
    alias: 'text/plain',
    mimeType: 'text/plain'
  }, {
    alias: 'text/css',
    mimeType: 'text/css'
  }, {
    alias: 'text/html',
    mimeType: 'text/html'
  }, {
    alias: 'text/javascript',
    mimeType: 'text/javascript'
  }, {
    alias: 'XML',
    mimeType: 'application/xml'
  }]
}, {
  title: '图片类型',
  values: [{
    alias: 'GIF',
    mimeType: 'image/gif'
  }, {
    alias: 'JPG JPEG',
    mimeType: 'image/jpeg'
  }, {
    alias: 'PNG',
    mimeType: 'image/png'
  }, {
    alias: 'SVG',
    mimeType: 'image/svg+xml'
  }]
}, {
  title: '音视频',
  values: [{
    alias: 'audio/webm',
    mimeType: 'audio/webm'
  }, {
    alias: 'video/webm',
    mimeType: 'video/webm'
  }, {
    alias: 'audio/ogg',
    mimeType: 'audio/ogg'
  }, {
    alias: 'video/ogg',
    mimeType: 'video/ogg'
  }, {
    alias: 'application/ogg',
    mimeType: 'application/ogg'
  }]
}]

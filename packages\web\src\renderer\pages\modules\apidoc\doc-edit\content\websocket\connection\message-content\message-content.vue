<template>
  <div class="message-content">
    <!-- 数据类型选择器 -->
    <div class="message-type-selector">
      <el-select v-model="messageType" size="small" @change="handleMessageTypeChange" class="type-selector">
        <el-option value="text" :label="t('文本')">
          <span class="option-content">
            <span>{{ t("文本") }}</span>
          </span>
        </el-option>
        <el-option value="json" :label="t('JSON')">
          <span class="option-content">
            <span>JSON</span>
          </span>
        </el-option>
        <el-option value="xml" :label="t('XML')">
          <span class="option-content">
            <span>XML</span>
          </span>
        </el-option>
        <el-option value="html" :label="t('HTML')">
          <span class="option-content">
            <span>HTML</span>
          </span>
        </el-option>
        <el-option value="binary-base64" :label="t('二进制(Base64)')">
          <span class="option-content">
            <span>{{ t("二进制(Base64)") }}</span>
          </span>
        </el-option>
        <el-option value="binary-hex" :label="t('二进制(Hex)')">
          <span class="option-content">
            <span>{{ t("二进制(Hex)") }}</span>
          </span>
        </el-option>
      </el-select>
    </div>

    <!-- 内容编辑器 -->
    <div class="content-editor">
      <SJsonEditor
          v-model="messageContent"
          :config="editorConfig"
          :auto-height="false"
        />
    </div>

    <!-- 操作按钮区域 -->
    <div class="content-actions">
      <div class="action-buttons">
        <el-tooltip
          :content="props.connectionState !== 'connected' ? t('等待连接') : ''"
          :disabled="props.connectionState === 'connected'"
          placement="top"
        >
          <el-button
            type="primary"
            :disabled="!messageContent.trim() || props.connectionState !== 'connected'"
            @click="handleSendMessage"
            :icon="Position"
          >
            {{ t("发送消息") }}
          </el-button>
        </el-tooltip>

        <el-checkbox
          v-model="sendAndClear"
          @change="handleSendAndClearChange"
        >
          {{ t("发送并清空") }}
        </el-checkbox>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useTranslation } from 'i18next-vue'
import { useRoute } from 'vue-router'
import { useApidocTas } from '@/store/apidoc/tabs'
import { webSocketNodeCache } from '@/cache/websocketNode'
import { ElMessage } from 'element-plus'
import {
  Position,
} from '@element-plus/icons-vue'
import SJsonEditor from '@/components/common/json-editor/g-json-editor.vue'

const props = withDefaults(defineProps<{
  connectionState?: string
  connectionId?: string
}>(), {
  connectionState: 'disconnected',
  connectionId: ''
})

const { t } = useTranslation()
const route = useRoute()
const apidocTabsStore = useApidocTas()

// 消息类型定义
type MessageType = 'text' | 'json' | 'xml' | 'html' | 'binary-base64' | 'binary-hex'

// 获取当前选中的tab
const currentSelectTab = computed(() => {
  const projectId = route.query.id as string
  const tabs = apidocTabsStore.tabs[projectId]
  const currentSelectTab = tabs?.find((tab) => tab.selected) || null
  return currentSelectTab
})

// 响应式数据
const messageContent = ref('')
const sendAndClear = ref(false)
const messageType = ref<MessageType>('text')

const editorConfig = computed(() => {
  switch (messageType.value) {
    case 'json':
      return { language: 'json' }
    case 'xml':
      return { language: 'xml' }
    case 'html':
      return { language: 'html' }
    case 'text':
    default:
      return { language: 'plaintext' }
  }
})


// 初始化状态
const initStates = () => {
  if (currentSelectTab.value) {
    sendAndClear.value = webSocketNodeCache.getWebSocketSendAndClearState(currentSelectTab.value._id)
    messageType.value = webSocketNodeCache.getWebSocketMessageType(currentSelectTab.value._id) as MessageType
  }
}

// 方法
const handleSendMessage = async () => {
  if (!messageContent.value.trim()) {
    ElMessage.warning(t('消息内容不能为空'))
    return
  }

  if (!props.connectionId) {
    ElMessage.error(t('WebSocket连接不存在'))
    return
  }

  if (props.connectionState !== 'connected') {
    ElMessage.error(t('WebSocket未连接'))
    return
  }

  try {
    const result = await window.electronAPI?.websocket.send(props.connectionId, messageContent.value)
    if (result?.success) {
      ElMessage.success(t('消息发送成功'))
      console.log('WebSocket消息发送成功:', messageContent.value)

      // 如果勾选了"发送并清空"，则清空消息内容
      if (sendAndClear.value) {
        messageContent.value = ''
      }
    } else {
      ElMessage.error(t('消息发送失败') + ': ' + (result?.error || t('未知错误')))
      console.error('WebSocket消息发送失败:', result?.error)
    }
  } catch (error) {
    ElMessage.error(t('消息发送异常'))
    console.error('WebSocket消息发送异常:', error)
  }
}

const handleSendAndClearChange = (value: boolean | string | number) => {
  const boolValue = Boolean(value)
  if (currentSelectTab.value) {
    webSocketNodeCache.setWebSocketSendAndClearState(currentSelectTab.value._id, boolValue)
  }
}

const handleMessageTypeChange = (value: MessageType) => {
  messageType.value = value
  if (currentSelectTab.value) {
    webSocketNodeCache.setWebSocketMessageType(currentSelectTab.value._id, value)
  }
}

const handleFormatJson = () => {
  if (messageType.value === 'json' && messageContent.value.trim()) {
    try {
      const parsed = JSON.parse(messageContent.value)
      messageContent.value = JSON.stringify(parsed, null, 2)
      ElMessage.success(t('JSON格式化成功'))
    } catch (error) {
      ElMessage.error(t('JSON格式无效，无法格式化'))
    }
  }
}

// 监听当前选中tab变化，重新加载状态
watch(currentSelectTab, (newTab) => {
  if (newTab) {
    sendAndClear.value = webSocketNodeCache.getWebSocketSendAndClearState(newTab._id)
    messageType.value = webSocketNodeCache.getWebSocketMessageType(newTab._id) as MessageType
  }
})

onMounted(() => {
  initStates()
})
</script>

<style lang="scss" scoped>
.message-content {
  padding: 0 16px;
  height: 100%;
  .message-type-selector {
    margin-bottom: 5px;
    .type-selector {
      width: 120px;
    }
  }

  .content-editor {
    height: calc(100vh - 300px);
    border: 1px solid var(--gray-400);
    .binary-editor {
      flex: 1;
      .binary-input {
        height: 100%;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          resize: none;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .content-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid var(--el-border-color-lighter);

    .action-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .action-options {
      display: flex;
      align-items: center;
    }
  }
}
</style>
